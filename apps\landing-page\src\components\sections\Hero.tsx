import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import {
  PlayIcon,
  StarIcon,
  UserGroupIcon,
  BriefcaseIcon,
  HeartIcon,
  SparklesIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/solid';

export default function Hero() {
  const { t } = useTranslation('landing');
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Mock stats data
  const stats = [
    {
      key: 'experts',
      value: '2,500+',
      icon: UserGroupIcon,
    },
    {
      key: 'projects',
      value: '15,000+',
      icon: BriefcaseIcon,
    },
    {
      key: 'clients',
      value: '8,000+',
      icon: HeartIcon,
    },
    {
      key: 'rating',
      value: '4.9/5',
      icon: StarIcon,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      ref={ref}
      className="relative min-h-screen flex items-center justify-center overflow-hidden py-20 lg:py-32"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(255, 215, 0, 0.08) 0%, transparent 60%),
          radial-gradient(ellipse at top, rgba(184, 134, 11, 0.1) 0%, transparent 50%),
          radial-gradient(ellipse at bottom, rgba(139, 105, 20, 0.1) 0%, transparent 50%),
          linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large Glass Orbs */}
        <motion.div
          animate={{
            y: [-30, 30, -30],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 left-10 w-32 h-32 rounded-full opacity-20"
          style={{
            background: 'rgba(255, 215, 0, 0.12)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 215, 0, 0.25)',
            boxShadow: '0 8px 32px rgba(255, 215, 0, 0.15)'
          }}
        />

        <motion.div
          animate={{
            y: [40, -40, 40],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 25, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-40 right-20 w-24 h-24 rounded-full opacity-15"
          style={{
            background: 'rgba(184, 134, 11, 0.1)',
            backdropFilter: 'blur(15px)',
            WebkitBackdropFilter: 'blur(15px)',
            border: '1px solid rgba(184, 134, 11, 0.2)',
            boxShadow: '0 8px 32px rgba(184, 134, 11, 0.12)'
          }}
        />

        <motion.div
          animate={{
            y: [-25, 25, -25],
            x: [-10, 10, -10],
            rotate: [0, 90, 180]
          }}
          transition={{ duration: 18, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-40 left-20 w-40 h-40 rounded-full opacity-10"
          style={{
            background: 'rgba(139, 105, 20, 0.08)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: '1px solid rgba(139, 105, 20, 0.15)',
            boxShadow: '0 8px 32px rgba(139, 105, 20, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.5
            }}
            className="absolute w-2 h-2 bg-white rounded-full"
            style={{
              left: `${10 + (i * 8)}%`,
              top: `${20 + (i * 5)}%`,
              filter: 'blur(1px)'
            }}
          />
        ))}
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        >
          {/* Enhanced Main Heading with Glass Effect */}
          <motion.div
            variants={itemVariants}
            className="relative mb-12"
          >
            <motion.h1
              className="heading-hero text-center text-white mb-6 relative z-10 px-8 py-6"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: 'easeOut' }}
              style={{ lineHeight: '1.1' }}
            >
              <span className="block gradient-text-gold-premium text-arabic-premium">
                {t('hero.title')}
              </span>
            </motion.h1>

            {/* Enhanced Glass backdrop for title */}
            <div
              className="absolute inset-0 -m-6 rounded-3xl opacity-30"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)',
                backdropFilter: 'blur(40px)',
                WebkitBackdropFilter: 'blur(40px)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            />
          </motion.div>

          {/* Enhanced Subtitle with Glass Container */}
          <motion.div
            variants={itemVariants}
            className="relative mb-12"
          >
            <motion.p
              className="text-2xl lg:text-3xl text-white/95 mb-10 max-w-4xl mx-auto leading-relaxed text-center relative z-10 px-8 py-6 text-arabic-premium"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              {t('hero.subtitle')}
            </motion.p>

            {/* Enhanced Glass backdrop for subtitle */}
            <div
              className="absolute inset-0 rounded-3xl opacity-40"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.12)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.08)'
              }}
            />
          </motion.div>

          {/* Enhanced Description */}
          <motion.p
            variants={itemVariants}
            className="text-xl text-white/80 mb-16 max-w-3xl mx-auto text-center leading-relaxed text-arabic px-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {t('hero.description')}
          </motion.p>

          {/* Enhanced CTA Buttons with Glass Effects */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row items-center justify-center gap-8 mb-20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.9 }}
          >
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
            >
              <Link
                href="/signup"
                className="btn-gold-premium text-xl font-bold px-12 py-6 w-full sm:w-auto inline-flex items-center justify-center gap-4 group text-arabic-premium"
              >
                <RocketLaunchIcon className="w-7 h-7 group-hover:rotate-12 transition-transform duration-300" />
                {t('hero.cta.primary')}
                <SparklesIcon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              </Link>
            </motion.div>

            <motion.button
              type="button"
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              className="glass-button text-white/95 text-xl font-semibold px-10 py-6 w-full sm:w-auto flex items-center justify-center gap-4 group text-arabic"
              onClick={() => {
                document.querySelector('#features')?.scrollIntoView({
                  behavior: 'smooth'
                });
              }}
            >
              <PlayIcon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              {t('hero.cta.secondary')}
            </motion.button>
          </motion.div>

          {/* Enhanced Stats with Glass Cards */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto mt-8"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.key}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 1.4 + (index * 0.1),
                  type: 'spring',
                  stiffness: 100
                }}
                whileHover={{
                  scale: 1.05,
                  y: -8,
                  transition: { duration: 0.2 }
                }}
                className="glass-card text-center p-8 group cursor-pointer"
              >
                <motion.div
                  className="flex items-center justify-center mb-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="w-16 h-16 rounded-2xl flex items-center justify-center relative overflow-hidden"
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <stat.icon className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300" />

                    {/* Shimmer effect */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                      style={{
                        background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                        backgroundSize: '200% 100%',
                        animation: 'glassShimmer 2s ease-in-out infinite'
                      }}
                    />
                  </div>
                </motion.div>

                <motion.div
                  className="text-4xl lg:text-5xl font-black text-white mb-3 group-hover:scale-105 transition-transform duration-300 font-cairo"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.6 + (index * 0.1) }}
                >
                  {stat.value}
                </motion.div>

                <motion.div
                  className="text-base text-white/80 font-semibold text-arabic"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.7 + (index * 0.1) }}
                >
                  {t(`hero.stats.${stat.key}`)}
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
